from json import JSONDecodeError
from typing import Any

from core import json


def validate_value_as_list_of_strings(value: str | list | None | Any) -> list[str]:
    if not value:
        # If value is empty - return empty list
        return []

    if isinstance(value, list):
        pass
    elif isinstance(value, str):
        # If the value is a string, attempt to parse it as JSON and then validate its items.
        try:
            value = json.loads(value)
        except JSONDecodeError as e:
            raise ValueError(f'Invalid JSON in suggested_prompts: {e}') from e
    else:
        raise ValueError(f'Invalid type for suggested_prompts: {type(value)}')

    if not isinstance(value, list):
        raise ValueError('Decoded suggested_prompts string must be a list')

    for item in value:
        if not isinstance(item, str):
            raise ValueError('Each item in suggested_prompts list must be a string')

    return value
