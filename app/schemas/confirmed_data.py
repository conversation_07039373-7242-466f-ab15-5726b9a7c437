import json

from core.schemas import CustomModel


__all__ = ['ConfirmedData']


class ConfirmedData(CustomModel):
    """Schema for user-confirmed qual data."""

    client_name: str | None = None
    ldmf_country: str | None = None
    date_intervals: tuple[str, str] | None = None
    objective_and_scope: str | None = None
    outcomes: str | None = None
    proposed_client_name: str | None = None  # For tracking client names during creation flow

    @classmethod
    def from_json_string(cls, json_str: str | None) -> 'ConfirmedData':
        """Create ConfirmedData from JSON string stored in database."""
        if not json_str:
            return cls()
        try:
            data = json.loads(json_str)
            return cls.model_validate(data)
        except (json.JSONDecodeError, ValueError):
            return cls()

    def to_json_string(self) -> str:
        """Convert ConfirmedData to JSON string for database storage."""
        return json.dumps(self.model_dump(), default=str)
