from datetime import datetime, timezone
import json
import logging
import os
from typing import Any, Dict, cast
from uuid import UUID

import azure.durable_functions as df
from openai import AsyncAzureOpenAI
from sqlalchemy.ext.asyncio import AsyncSession

from durable_functions.application.config import settings
from durable_functions.application.db import async_session_local
from durable_functions.repositories import OpenAIRepository
from durable_functions.utils import (
    ActivityName,
    BlobStorageHelper,
    DocumentIntelligenceHelper,
    ExctractStatus,
    ExtractedDataMerger,
    OrchestratorInputType,
    RecursiveChunkingStrategy,
    SignalRApiClient,
)
from durable_functions.utils.models import FinalExtractionDataResults, LLMExtractedDataResult
from repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    DocumentQueueRepository,
    ExtractedDataRepository,
    ProcessingMessageRepository,
)
from schemas import ProcessingStatusUpdatePayload
from schemas.extracted_data import ExtractedData

from .models import (
    ChunkDocumentActivityInput,
    ChunkDocumentActivityOutput,
    ExtractDataActivityInput,
    ExtractDocumentTextActivityInput,
    ExtractDocumentTextActivityOutput,
    ExtractDocumentTextActivityOutputFailed,
    ReadPromptActivityInput,
    SaveExtractionDataActivityInput,
    SendNotificationActivityInput,
    SendQueueMessageActivityInput,
    UpdateProcessingStatusActivityInput,
)


logger = logging.getLogger(__name__)

bp = df.Blueprint()


@bp.activity_trigger('document', ActivityName.ExtractDocumentText)
def extract_document_text(
    document: ExtractDocumentTextActivityInput,
) -> ExtractDocumentTextActivityOutput | ExtractDocumentTextActivityOutputFailed:
    """
    Activity function to extract text from a document using Document Intelligence.
    """
    try:
        file_name = document.file_name
        logger.info(f'Extracting text from document {file_name} for message {document.message_id}')

        blob_helper = BlobStorageHelper()
        document_bytes = blob_helper.get_blob_from_url(document.blob_url)

        try:
            doc_intelligence = DocumentIntelligenceHelper()
            extraction_result = doc_intelligence.extract_text_from_document(document_bytes)
        except Exception as e:
            logger.error(f'Document Intelligence extraction failed: {str(e)}')
            return ExtractDocumentTextActivityOutputFailed(
                message_id=document.message_id,
                file_name=file_name,
                error=f'Document extraction failed: {str(e)}',
                status=ExctractStatus.Failed,
            )

        base_name = os.path.splitext(file_name)[0]  # type: ignore
        extract_filename = f'{base_name}.json'
        extraction_path = f'extracted/{document.message_id}/{extract_filename}'
        extraction_url = blob_helper.upload_json(extraction_path, extraction_result)

        return ExtractDocumentTextActivityOutput(
            message_id=document.message_id,
            file_name=file_name,
            extraction_url=extraction_url,
            text_content=extraction_result['text'],
            metadata={
                **extraction_result['metadata'],
                'original_blob_url': document.blob_url,
            },
            status=ExctractStatus.Success,
        )

    except Exception:
        logger.exception('Error extracting document text')
        raise


@bp.activity_trigger('updates', ActivityName.UpdateProcessingStatus)
async def update_processing_status(updates: UpdateProcessingStatusActivityInput) -> Dict[str, Any]:
    """
    Activity function to update the file processing status.

    This function demonstrates how to integrate with FastAPI repositories.
    Currently logs the status update, but can be enhanced to use database repositories.
    """
    try:
        logger.info(f'Updating processing status for message_id {updates.message_id}: {updates.status}')

        async with cast(AsyncSession, async_session_local()) as session:
            process_message_repo = ProcessingMessageRepository(session)

            # Update message processing status in database
            await process_message_repo.create(
                data=ProcessingStatusUpdatePayload(
                    message_id=UUID(updates.message_id),
                    status=updates.status,
                    message=updates.message,
                    metadata=updates.metadata,
                )
            )
            await session.commit()

        logger.info(f'Processing status update: {updates.status} - {updates.message}')
        if updates.metadata:
            logger.info(f'Metadata: {updates.metadata}')

        return {
            'status': 'success',
            'message_id': updates.message_id,
            'processing_status': updates.status,
            'message': updates.message,
            'metadata': updates.metadata,
        }

    except Exception:
        logger.exception('Error updating processing status')
        raise


@bp.activity_trigger('notification', ActivityName.SendNotification)
async def send_notification(notification: SendNotificationActivityInput) -> None:
    """
    Activity function to send a notification via SignalR.

    Args:
        notification: Notification data including event type, data, and optional signalr_user_id

    Returns:
        None
    """
    try:
        logger.info(f'Sending notification event: {notification.event_type}')
        signalr_client = SignalRApiClient()

        # Pass the signalr_user_id to the send_notification method
        await signalr_client.send_notification(
            event_type=notification.event_type, data=notification.data, user_id=notification.signalr_user_id
        )

    except Exception:
        logger.exception('Error sending notification')
        raise


@bp.activity_trigger('extraction', ActivityName.ChunkDocument)
def chunk_document(extraction: ChunkDocumentActivityInput) -> ChunkDocumentActivityOutput:
    """
    Activity function to chunk a document.
    """
    try:
        message_id = extraction.message_id
        file_name = extraction.file_name
        text_content = extraction.text_content
        metadata = extraction.metadata

        logger.info(f'Chunking document {file_name} for message {message_id}')

        chunking_strategy = RecursiveChunkingStrategy()
        document_metadata = {
            'message_id': message_id,
            'file_name': file_name,
        }
        for key, value in metadata.items():
            document_metadata[key] = value

        chunks = chunking_strategy.chunk_document(text_content, document_metadata)

        blob_helper = BlobStorageHelper()
        chunk_urls = []

        for chunk in chunks:
            base_name = os.path.splitext(file_name)[0]  # type: ignore
            chunk_filename = f'{base_name}_chunk_{chunk["chunk_index"]}.json'
            chunk_path = f'chunks/{message_id}/{chunk_filename}'
            chunk_url = blob_helper.upload_json(chunk_path, chunk)
            chunk_urls.append({'chunk_index': chunk['chunk_index'], 'chunk_id': chunk['chunk_id'], 'url': chunk_url})

        return ChunkDocumentActivityOutput(
            message_id=message_id,
            file_name=file_name,
            chunk_count=len(chunks),
            chunk_urls=chunk_urls,
        )

    except Exception:
        logger.exception('Error chunking document')
        raise


@bp.activity_trigger('message', ActivityName.SendQueueMessage)
async def send_queue_message(message: SendQueueMessageActivityInput) -> None:
    if message.input_type == OrchestratorInputType.Prompt:
        queue_name = settings.QUEUE_SETTINGS.PROMPT_PROCESSING_QUEUE_CHUNKED
    else:
        queue_name = settings.QUEUE_SETTINGS.DOCUMENT_PROCESSING_QUEUE_CHUNKED

    try:
        logger.info(f'Sending queue message: {message}')

        queue_repo = DocumentQueueRepository(
            connection_string=settings.QUEUE_SETTINGS.CONNECTION_STRING,
            queue_name=queue_name,
        )
        await queue_repo.send_message(message.model_dump())
    except Exception as e:
        logger.error(f'Error sending notification: {str(e)}')
        raise


@bp.activity_trigger('prompt', ActivityName.ReadPrompt)
async def read_prompt(prompt: ReadPromptActivityInput) -> str:
    try:
        prompt_url = prompt.prompt_url
        logger.info(f'Reading prompt from {prompt_url}')
        blob_helper = BlobStorageHelper()
        prompt_text = blob_helper.get_blob_from_url(prompt_url).decode('utf-8')
        return prompt_text
    except Exception:
        logger.exception('Error reading prompt')
        raise


@bp.activity_trigger('extraction', ActivityName.ExtractData)
async def extract_data(extraction: ExtractDataActivityInput) -> LLMExtractedDataResult:
    """
    Activity function to extract data from a document.
    """
    blob_helper = BlobStorageHelper()

    async with AsyncAzureOpenAI(
        azure_endpoint=settings.openai.endpoint,
        api_key=settings.openai.key,
        api_version=settings.openai.api_version,
    ) as client:
        openai_repo = OpenAIRepository(client=client)

        try:
            if extraction.chunk_url:
                chunk_url = extraction.chunk_url
                chunk_data = blob_helper.download_json(chunk_url)
                text = chunk_data.get('text')
            elif extraction.text_content:
                text = extraction.text_content
            else:
                raise ValueError('No chunk_url or text_content provided')

            if text := text.strip():
                logger.info('Extracting data from text')
                return await openai_repo.extract_data(text)
            else:
                return LLMExtractedDataResult()
        except Exception:
            logger.exception('Error extracting data')
            raise


@bp.activity_trigger('merging', ActivityName.MergeExtractionData)
async def merge_extraction_data(merging) -> FinalExtractionDataResults:
    try:
        return ExtractedDataMerger.merge_results(merging)
    except Exception:
        logger.exception('Error merging extraction data')
        raise


@bp.activity_trigger('saving', ActivityName.SaveExtractionData)
async def save_extraction_data(saving: SaveExtractionDataActivityInput):
    """
    Activity function to save the extraction data to the database.
    """
    logger.info(f'Saving extraction data: {saving}')
    async with cast(AsyncSession, async_session_local()) as session:
        extracted_data_repo = ExtractedDataRepository(session, ConversationRepository(session))
        message_repo = ConversationMessageRepository(session, ConversationRepository(session))

        message_id = UUID(saving.message_id)
        logger.info(f'Message ID: {message_id}')
        message = await message_repo.get(message_id)
        if not message:
            logger.warning(f'Message not found for message_id: {message_id}')
            raise ValueError(f'Message not found for message_id: {message_id}')

        client_name = json.dumps(saving.extraction_data.client_names) if saving.extraction_data.client_names else '[]'
        ldmf_country = (
            json.dumps(saving.extraction_data.lead_member_countries)
            if saving.extraction_data.lead_member_countries
            else '[]'
        )
        if saving.extraction_data.periods:
            # get first period
            start_date = saving.extraction_data.periods[0].start_date
            end_date = saving.extraction_data.periods[0].end_date
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
        else:
            start_date = None
            end_date = None

        extracted_data = ExtractedData(
            ConversationPublicId=message.conversation_id,
            DataSourceType=saving.data_source_type,
            ClientName=client_name,
            LDMFCountry=ldmf_country,
            StartDate=start_date,
            EndDate=end_date,
            ObjectiveAndScope=saving.extraction_data.objective_and_scope,
            Outcomes=saving.extraction_data.outcomes,
            CreatedAt=datetime.now(timezone.utc),
        )
        await extracted_data_repo.update(extracted_data)
        await session.commit()
