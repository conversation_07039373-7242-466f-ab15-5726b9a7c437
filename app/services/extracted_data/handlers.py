from abc import ABC, abstractmethod
import asyncio

from constants.extracted_data import <PERSON><PERSON><PERSON><PERSON>
from constants.message import (
    CLIENT_NAME_MULTIPLE_OPTIONS,
    CLIENT_NAME_SINGLE_CONFIRMATION,
    CLIENT_NOT_FOUND_PROMPT,
    NEED_INFO_CLIENT_NAME,
    NEED_INFO_OUTCOMES,
)
from repositories import QualsClientsRepository
from schemas import AggregatedData, ClientSearchRequest, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData


__all__ = [
    'ClientNameHandler',
    'LDMFCountryHandler',
    'DateIntervalsHandler',
    'ObjectiveHandler',
    'OutcomesHandler',
]


class BaseFieldHandler(ABC):
    """
    Abstract base class for handling missing required fields and generating prompts.
    """

    @abstractmethod
    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        pass


class ClientNameHandler(BaseFieldHandler):
    """Handler for RequiredField.CLIENT_INFO."""

    def __init__(self, quals_clients_repository: QualsClientsRepository):
        self.quals_clients_repository = quals_clients_repository

    async def check_and_get_response(
        self,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
    ) -> FieldHandlerResponse:
        # Check if client name is already confirmed
        if confirmed_data.client_name is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )

        # Check if we have multiple client names in aggregated data
        if len(aggregated_data.client_name) > 1:
            # Search for each client name using the API
            try:
                # Create search requests for each client name
                search_requests = [
                    ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
                    for client_name in aggregated_data.client_name
                ]

                # Perform concurrent searches
                search_results = await asyncio.gather(
                    *[self.quals_clients_repository.search_clients(request) for request in search_requests],
                    return_exceptions=True,
                )

                # Collect found client names
                found_client_names = []
                for i, result in enumerate(search_results):
                    if not isinstance(result, Exception) and hasattr(result, 'clients') and result.clients:
                        # Use the original client name from aggregated data if API found results
                        found_client_names.append(aggregated_data.client_name[i])

                if found_client_names:
                    return FieldHandlerResponse(
                        needs_confirmation=True,
                        field_status=FieldStatus.PENDING_CONFIRMATION,
                        system_message=CLIENT_NAME_MULTIPLE_OPTIONS,
                        next_expected_field=None,
                        options=found_client_names,
                    )
            except Exception:
                # If API search fails, fall back to showing all options
                pass

            # Fallback: show all client names as options
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MULTIPLE,
                system_message=CLIENT_NAME_MULTIPLE_OPTIONS,
                next_expected_field=None,
                options=aggregated_data.client_name,
            )

        # Check if we have a single client name
        elif len(aggregated_data.client_name) == 1:
            client_name = aggregated_data.client_name[0]

            # Search for the single client name using the API
            try:
                search_request = ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
                search_result = await self.quals_clients_repository.search_clients(search_request)

                # If exactly ONE confident match is found, automatically proceed (no user confirmation needed)
                if search_result.clients and len(search_result.clients) == 1:
                    # Auto-confirm the client name - return as confirmed
                    # Note: The auto-confirmation logic will be handled in the service layer
                    return FieldHandlerResponse(
                        needs_confirmation=False,
                        field_status=FieldStatus.CONFIRMED,
                        system_message=None,
                        next_expected_field=None,
                        options=[],  # No further selection required
                    )

                # If multiple matches or no matches, ask for confirmation
                elif search_result.clients and len(search_result.clients) > 1:
                    # Multiple matches found - use the original client name but ask for confirmation
                    return FieldHandlerResponse(
                        needs_confirmation=True,
                        field_status=FieldStatus.MULTIPLE,
                        system_message=CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name=client_name),
                        next_expected_field=None,
                        options=[client_name],
                    )
                else:
                    # No matches found - ask if user wants to add as new client
                    return FieldHandlerResponse(
                        needs_confirmation=True,
                        field_status=FieldStatus.SINGLE,
                        system_message=CLIENT_NOT_FOUND_PROMPT.format(client_name=client_name),
                        next_expected_field=None,
                        options=[client_name],
                    )

            except Exception:
                # If API search fails, fall back to asking for confirmation
                return FieldHandlerResponse(
                    needs_confirmation=True,
                    field_status=FieldStatus.SINGLE,
                    system_message=CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name=client_name),
                    next_expected_field=None,
                    options=[client_name],
                )

        # No client names found
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_CLIENT_NAME,
                next_expected_field=None,
            )


class LDMFCountryHandler(BaseFieldHandler):
    """Handler for RequiredField.LDMF_COUNTRY."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.MISSING,
            system_message='Implement ldmf handler',
            next_expected_field=None,
        )


class DateIntervalsHandler(BaseFieldHandler):
    """Handler for RequiredField.ENGAGEMENT_DATES."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.MISSING,
            system_message='Implement date intervals handler',
            next_expected_field=None,
        )


class ObjectiveHandler(BaseFieldHandler):
    """Handler for RequiredField.OBJECTIVE_SCOPE."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
            needs_confirmation=False,
            field_status=FieldStatus.MISSING,
            system_message='Implement objective and scope handler',
            next_expected_field=None,
        )


class OutcomesHandler(BaseFieldHandler):
    """Handler for RequiredField.OUTCOMES."""

    async def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        # Check if outcomes are confirmed
        if confirmed_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if outcomes are in aggregated data
        elif aggregated_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=f'found this outcomes: {aggregated_data.outcomes} can you confirm?',
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message=NEED_INFO_OUTCOMES,
                next_expected_field=None,
            )
