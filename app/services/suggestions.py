from dataclasses import dataclass
import logging
from uuid import UUID

from fastapi import UploadFile

from constants.message import (
    ConversationMessageIntention,
    SuggestedUserPrompt,
)
from services.extracted_data import ExtractedDataService
from services.kx_dash import KXDashService


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class SuggestedPromptsGenerator:
    """Detector for conversation message suggested prompts."""

    conversation_id: UUID
    user_message: str
    intention: ConversationMessageIntention
    kx_dash_service: KXDashService
    extracted_data_service: ExtractedDataService
    files: list[UploadFile] | None = None

    async def run(self) -> list[SuggestedUserPrompt]:
        """Run the suggested prompts generator."""

        user_provides_prompt_or_files = await self._user_provides_prompt_or_files()
        user_selects_brief_description = await self._user_selects_brief_description()

        user_has_dash_tasks = await self._user_has_dash_tasks()
        user_navigates_from_dash_task = await self._user_navigates_from_dash_task()
        user_provided_all_required_fields = await self._user_provided_all_required_fields()

        ai_detected_multiple_client_name_variations = await self._ai_detected_multiple_client_name_variations()
        ai_retrieves_single_client_name = await self._ai_retrieves_single_client_name()
        ai_couldnt_find_provided_client_name = await self._ai_couldnt_find_provided_client_name()

        suggested_prompts = []

        ##################
        # USE CASE 1, 2
        if user_has_dash_tasks and user_navigates_from_dash_task:
            """
            1. GIVEN the user has assigned one or more than one dash task(s)
            2. AND the user navigates from the Dash task in the KX Dash app/KX Dash actionable email/Home page
            3. WHEN the user navigates to the Prompt page
            4. VERIFY that the user can see the "No, create a new qual" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.NO_CREATE_NEW_QUAL)

        ##################
        # USE CASE 3, 4, 5
        if user_selects_brief_description:
            """
            1. GIVEN the user is on the Prompt page
            2. WHEN the user selects the option "Write a brief description"
            3. VERIFY that the user can see the "Show me an example prompt" as a clickable option under the message
            """
            suggested_prompts.append(SuggestedUserPrompt.SHOW_ME_AN_EXAMPLE_PROMPT)

        ##################
        # USE CASE 6, 7
        if user_provided_all_required_fields:
            """
            1. GIVEN the user is on the Prompt page
            2. WHEN the user provides relevant info for all required fields to generate the qual
            3. VERIFY that the user can see the "No, create my qual" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.NO_CREATE_MY_QUAL)

        ##################
        # USE CASE 8, 9
        if user_provides_prompt_or_files and ai_detected_multiple_client_name_variations:
            """
            1. GIVEN the user navigates to the Prompt page
            2. AND the user provides a prompt/uploads a document
            3. WHEN the AI detects a few variations of the Client name
            4. VERIFY that the user can see the "Enter a new client" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.ENTER_A_NEW_CLIENT)

        ##################
        # USE CASE 10, 11
        if user_provides_prompt_or_files and ai_retrieves_single_client_name:
            """
            1. GIVEN the user navigates to the Prompt page
            2. AND the user provides a prompt/uploads a document
            3. WHEN the AI retrieves a single result of the client's name
            4. VERIFY that the user can see the "Yes, this is correct" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.YES_THIS_IS_CORRECT)

        ##################
        # USE CASE 12, 13
        if user_provides_prompt_or_files and (ai_retrieves_single_client_name or ai_couldnt_find_provided_client_name):
            """
            1. GIVEN the user navigates to the Prompt page
            2. AND the user provides a prompt/uploads a document
            3. WHEN the AI retrieves a single result of the client's name
            4. OR AI couldn't find the provided client name in the system
            5. VERIFY that the user can see the "No, I'll enter the client name" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME)

        ##################
        # USE CASE 14, 15
        if user_provides_prompt_or_files and ai_couldnt_find_provided_client_name:
            """
            1. GIVEN the user navigates to the Prompt page
            2. AND the user provides a prompt/uploads a document
            3. WHEN the AI couldn't find the provided client name in the system (unique)
            4. VERIFY that the user can see the "Yes" suggested prompt above the input
            """
            suggested_prompts.append(SuggestedUserPrompt.YES)

        return suggested_prompts

    ############################################################
    # utils

    async def _user_provides_prompt_or_files(self) -> bool:
        """Check if user provides prompt or files."""
        return bool(self.user_message.strip()) or bool(self.files)

    async def _user_selects_brief_description(self) -> bool:
        """Check if user selects brief description."""
        return self.intention == ConversationMessageIntention.UNCERTAINTY

    async def _user_has_dash_tasks(self) -> bool:
        """Check if user has dash tasks."""
        # TODO: fix extracted_data_service calls
        return False
        # dash_tasks = await self.kx_dash_service.list()
        # return bool(dash_tasks)

    async def _user_navigates_from_dash_task(self) -> bool:
        """Check if user navigates from dash task."""
        # TODO: fix extracted_data_service calls
        return False
        # extracted_data = await self.extracted_data_service.get(
        #     conversation_id=self.conversation_id,
        #     data_source_type=DataSourceType.KX_DASH,
        # )
        # return bool(extracted_data)

    async def _user_provided_all_required_fields(self) -> bool:
        """Check if all required fields are filled for qual generation."""
        return False
        # TODO: fix extracted_data_service calls
        # aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)

        # return all(
        #     [
        #         bool(aggregated_data.client_name),
        #         bool(aggregated_data.ldmf_country),
        #         bool(aggregated_data.date_intervals),
        #         bool(aggregated_data.objective_and_scope),
        #         bool(aggregated_data.outcomes),
        #     ]
        # )

    async def _ai_detected_multiple_client_name_variations(self) -> bool:
        """Check if multiple client name variations were detected."""
        # TODO: fix extracted_data_service calls
        return False
        # aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)
        # return bool(aggregated_data.client_name and len(aggregated_data.client_name) > 1)

    async def _ai_retrieves_single_client_name(self) -> bool:
        """Check if a single client name was retrieved."""
        # TODO: fix extracted_data_service calls
        return False
        # aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)
        # return bool(aggregated_data.client_name and len(aggregated_data.client_name) == 1)

    async def _ai_couldnt_find_provided_client_name(self) -> bool:
        """Check if no client name was found in the extracted data."""
        # TODO: fix extracted_data_service calls
        return False
        # aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)
        # return not bool(aggregated_data.client_name)
