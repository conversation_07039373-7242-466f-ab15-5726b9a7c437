from dataclasses import dataclass
import logging
import re
from typing import Any, Optional
from uuid import UUID

from fastapi import UploadFile

from constants.extracted_data import ConversationState, MissingDataStatus
from constants.message import (
    BRIEF_DESCRIPTION_REPLY,
    CLIENT_CREATION_CONFIRMED,
    CLIENT_NAME_CONFIRMED,
    CLIENT_NAME_SINGLE_CONFIRMATION,
    CLIENT_NOT_FOUND_PROMPT,
    EXAMPLE_REPLY,
    UNDEFINED_REPLY,
    ConversationMessageIntention,
)
from exceptions import EntityNotFoundError
from repositories import ConversationRepository
from schemas import (
    ClientCreateRequest,
    ClientSearchRequest,
    ConversationMessageIntentClassifierServiceResponse,
    ConversationMessageProcessingResult,
)

from .extracted_data import ExtractedDataService
from .intent_classifier import IntentClassifierService


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class UncertaintyProcessingResult:
    """Result of uncertainty intention processing."""

    system_reply: str
    missing_data_status: MissingDataStatus
    next_expected_field: str | None = None
    missing_fields: list[str] | None = None
    options: list[str] | None = None
    conversation_state: Optional[ConversationState] = None


@dataclass(frozen=True)
class ConversationMessageProcessor:
    """Processor for conversation message intention."""

    conversation_id: UUID
    user_message: str
    intent_classifier_service: IntentClassifierService
    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository
    files: list[UploadFile] | None = None

    async def _get_intent(self) -> ConversationMessageIntention:
        """Intent getter."""
        intent_classification_response: ConversationMessageIntentClassifierServiceResponse = (
            await self.intent_classifier_service.classify_intent(
                user_message=self.user_message,
                response_cls=ConversationMessageIntentClassifierServiceResponse,
            )
        )
        intention = intent_classification_response.intention
        logger.info(f'Classified intention: {intention}')
        return intention

    async def run(self) -> ConversationMessageProcessingResult:
        """Process the intention."""

        if not self.user_message:
            raise ValueError('You cannot classify intent with empty user message.')

        intention = await self._get_intent()

        if intention == ConversationMessageIntention.UNDEFINED:
            data = self._process_undefined()
        elif intention == ConversationMessageIntention.GENERATE_QUAL:
            data = self._generate_qual()
        elif intention == ConversationMessageIntention.EXTRACTION:
            data = self._extract_data()
        elif intention == ConversationMessageIntention.EXAMPLE:
            data = self._example_help()
        elif intention == ConversationMessageIntention.DASH_DISCARD:
            data = self._dash_discard()
        elif intention == ConversationMessageIntention.UNCERTAINTY:
            data = await self._uncertainty()
        elif intention == ConversationMessageIntention.NEED_CONTEXT:
            data = await self._uncertainty()
        else:
            raise NotImplementedError(f'Intent {intention} not implemented')

        # If the data is an instance of UncertaintyProcessingResult, convert it to a dict
        # for compatibility with ConversationMessageProcessingResult
        if isinstance(data, UncertaintyProcessingResult):
            system_reply = data.system_reply
            # Convert dataclass to dict, excluding None values
            data_dict = {k: v for k, v in data.__dict__.items() if v is not None and k != 'system_reply'}
        else:
            system_reply = data.pop('system_reply')
            data_dict = data

        return ConversationMessageProcessingResult(
            intention=intention,
            system_reply=system_reply,
            data=data_dict,
        )

    def _process_undefined(self) -> dict[str, Any]:
        return {'system_reply': UNDEFINED_REPLY}

    def _generate_qual(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to generate a qual
        # ...
        system_reply = f'Finished processing `_generate_qual` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _extract_data(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to extract data
        # ...
        system_reply = f'Finished processing `_extract_data` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _dash_show_more(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to show more dash tasks
        # ...
        system_reply = f'Finished processing `_dash_show_more` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _dash_discard(self) -> dict[str, Any]:
        # TODO(TASK-???): Call a service to discard dash tasks
        # ...
        system_reply = f'Finished processing `_dash_discard` job for user message {self.user_message}'
        return {
            'system_reply': system_reply,
        }

    def _example_help(self) -> dict[str, Any]:
        return {'system_reply': EXAMPLE_REPLY}

    async def _uncertainty(self) -> UncertaintyProcessingResult:
        """
        Handle uncertainty intention with progressive data collection.

        This method triggers the missing data collection flow, checking what
        information is still needed and guiding the user through confirmation.
        """
        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(self.conversation_id))

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)

            # Check if user is providing manual input for client name
            if (
                str(conversation.State) == ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and self.user_message.strip()
            ):
                # Check if user is responding to client creation prompt
                if confirmed_data.proposed_client_name is not None:
                    return await self._handle_client_creation_response(
                        proposed_client_name=confirmed_data.proposed_client_name
                    )

                # Extract client name from user message and process it
                client_name = self.user_message.strip()
                return await self._handle_client_name_input(client_name)

            # Check if user is providing a new client name in their message (not in COLLECTING_CLIENT_NAME state)
            elif (
                str(conversation.State) != ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and self.user_message.strip()
            ):
                # Try to detect client name from user message
                detected_client_name = await self._detect_client_name_from_message(self.user_message)
                if detected_client_name:
                    return await self._handle_client_name_input(detected_client_name)

            # Check what data is missing using the enhanced service
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=self.conversation_id, confirmed_data=confirmed_data
            )

            # Update conversation state based on the response
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )
                return UncertaintyProcessingResult(
                    system_reply=missing_data_response.message or '',
                    missing_data_status=missing_data_response.status,
                    next_expected_field=missing_data_response.next_expected_field,
                    missing_fields=missing_data_response.missing_fields,
                    options=missing_data_response.options,
                    conversation_state=missing_data_response.conversation_state,
                )

            elif missing_data_response.status == MissingDataStatus.DATA_COMPLETE:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )
                return UncertaintyProcessingResult(
                    system_reply=BRIEF_DESCRIPTION_REPLY,
                    missing_data_status=missing_data_response.status,
                )

            else:  # error status
                logger.error(
                    'Error in missing data collection for conversation %s: %s',
                    self.conversation_id,
                    missing_data_response.message,
                )
                return UncertaintyProcessingResult(
                    system_reply=BRIEF_DESCRIPTION_REPLY,
                    missing_data_status=MissingDataStatus.ERROR,
                )

        except Exception as e:
            logger.error('Exception in _uncertainty method for conversation %s: %s', self.conversation_id, e)
            # Fallback to original behavior on any error
            return UncertaintyProcessingResult(
                system_reply=BRIEF_DESCRIPTION_REPLY, missing_data_status=MissingDataStatus.ERROR
            )

    async def _detect_client_name_from_message(self, message: str) -> str | None:
        """
        Detect client name from user message using simple heuristics.

        This is a basic implementation that looks for patterns like:
        - "client is X"
        - "working with X"
        - "for X company"
        etc.

        Args:
            message: The user message to analyze

        Returns:
            Detected client name or None if not found
        """
        message_lower = message.lower().strip()

        # Simple patterns to detect client names
        patterns = [
            r'client\s+is\s+([^.!?]+)',
            r'working\s+with\s+([^.!?]+)',
            r'for\s+([^.!?]+)\s+company',
            r'at\s+([^.!?]+)\s+company',
            r'client\s+name\s+is\s+([^.!?]+)',
            r'client:\s*([^.!?]+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, message_lower)
            if match:
                client_name = match.group(1).strip()
                # Clean up the client name (remove common words)
                client_name = re.sub(r'\b(company|corp|corporation|inc|ltd|llc)\b', '', client_name).strip()
                if len(client_name) > 2:  # Minimum length check
                    return client_name.title()  # Capitalize properly

        return None

    async def _handle_client_name_input(self, client_name: str) -> UncertaintyProcessingResult:
        """
        Handle client name input by searching the API and determining next steps.

        Args:
            client_name: The client name to process

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """

        try:
            # Search for the client name using the quals client repository
            search_request = ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
            search_result = await self.extracted_data_service.quals_clients_repository.search_clients(search_request)

            if search_result.clients and len(search_result.clients) == 1:
                # Exactly one match found - auto-confirm
                confirmed_client_name = search_result.clients[0].name
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name='client_name',
                    field_value=confirmed_client_name,
                    state=ConversationState.COLLECTING_COUNTRY,
                )

                confirmation_message = CLIENT_NAME_CONFIRMED.format(client_name=confirmed_client_name)
                return UncertaintyProcessingResult(
                    system_reply=confirmation_message,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                )

            elif search_result.clients and len(search_result.clients) > 1:
                # Multiple matches found - ask for confirmation with the original name
                await self.conversation_repository.update_state(
                    self.conversation_id, ConversationState.COLLECTING_CLIENT_NAME
                )

                confirmation_message = CLIENT_NAME_SINGLE_CONFIRMATION.format(client_name=client_name)
                return UncertaintyProcessingResult(
                    system_reply=confirmation_message,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[client_name],
                )

            else:
                # No matches found - ask if user wants to add as new client
                await self.conversation_repository.update_state(
                    self.conversation_id, ConversationState.COLLECTING_CLIENT_NAME
                )

                # Store the proposed client name in confirmed_data for session persistence
                confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
                confirmed_data.proposed_client_name = client_name
                await self.conversation_repository.update_confirmed_data_and_state(
                    public_id=self.conversation_id,
                    confirmed_data=confirmed_data,
                    state=ConversationState.COLLECTING_CLIENT_NAME,
                )

                confirmation_message = CLIENT_NOT_FOUND_PROMPT.format(client_name=client_name)
                return UncertaintyProcessingResult(
                    system_reply=confirmation_message,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[client_name],
                )

        except Exception as e:
            logger.error('Error handling client name input for conversation %s: %s', self.conversation_id, e)
            # Fallback to simple confirmation
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=self.conversation_id,
                field_name='client_name',
                field_value=client_name,
                state=ConversationState.COLLECTING_COUNTRY,
            )

            confirmation_message = CLIENT_NAME_CONFIRMED.format(client_name=client_name)
            return UncertaintyProcessingResult(
                system_reply=confirmation_message,
                missing_data_status=MissingDataStatus.MISSING_DATA,
            )

    async def _handle_client_creation_response(self, proposed_client_name: str) -> UncertaintyProcessingResult:
        """
        Handle user response to client creation prompt.

        Args:
            proposed_client_name: The client name that was proposed for creation

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        user_response = self.user_message.strip().lower()

        # Check if user confirms adding the new client
        if any(keyword in user_response for keyword in ['yes', 'add', 'create', 'confirm']):
            try:
                # Create the new client using the repository

                create_request = ClientCreateRequest(name=proposed_client_name)
                create_result = await self.extracted_data_service.quals_clients_repository.create_client(create_request)

                if create_result.success:
                    # Update confirmed data with the new client name and clear proposed name
                    await self.extracted_data_service.update_confirmed_data(
                        conversation_id=self.conversation_id,
                        field_name='client_name',
                        field_value=proposed_client_name,
                        state=ConversationState.COLLECTING_COUNTRY,
                    )

                    # Clear the proposed client name
                    confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
                    confirmed_data.proposed_client_name = None
                    await self.conversation_repository.update_confirmed_data_and_state(
                        public_id=self.conversation_id,
                        confirmed_data=confirmed_data,
                        state=ConversationState.COLLECTING_CLIENT_NAME,
                    )

                    confirmation_message = CLIENT_CREATION_CONFIRMED.format(client_name=proposed_client_name)
                    return UncertaintyProcessingResult(
                        system_reply=confirmation_message,
                        missing_data_status=MissingDataStatus.MISSING_DATA,
                    )
                else:
                    # Client creation failed
                    return UncertaintyProcessingResult(
                        system_reply=f"Sorry, I couldn't add '{proposed_client_name}' as a new client. Please try a different name.",
                        missing_data_status=MissingDataStatus.MISSING_DATA,
                        conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    )

            except Exception as e:
                logger.error(
                    'Error creating client %s for conversation %s: %s', proposed_client_name, self.conversation_id, e
                )
                return UncertaintyProcessingResult(
                    system_reply=f"Sorry, I encountered an error while adding '{proposed_client_name}'. Please try again.",
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                )

        # Check if user provides a different client name
        elif any(keyword in user_response for keyword in ['no', 'different', 'another', 'enter']):
            # Clear the proposed client name and treat the message as a new client name input
            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
            confirmed_data.proposed_client_name = None
            await self.conversation_repository.update_confirmed_data_and_state(
                public_id=self.conversation_id,
                confirmed_data=confirmed_data,
                state=ConversationState.COLLECTING_CLIENT_NAME,
            )

            # Check if the user provided a new client name in their response
            detected_client_name = await self._detect_client_name_from_message(self.user_message)
            if detected_client_name:
                return await self._handle_client_name_input(detected_client_name)
            else:
                # Ask for client name input
                return UncertaintyProcessingResult(
                    system_reply='Please provide the client name you would like to use.',
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                )

        # If the response contains a potential client name, treat it as a new client name
        else:
            detected_client_name = await self._detect_client_name_from_message(self.user_message)
            if detected_client_name:
                # Clear the proposed client name and process the new one
                confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
                confirmed_data.proposed_client_name = None
                await self.conversation_repository.update_confirmed_data_and_state(
                    public_id=self.conversation_id,
                    confirmed_data=confirmed_data,
                    state=ConversationState.COLLECTING_CLIENT_NAME,
                )
                return await self._handle_client_name_input(detected_client_name)
            else:
                # Unclear response - ask for clarification
                return UncertaintyProcessingResult(
                    system_reply=f"I'm not sure what you mean. Would you like to add '{proposed_client_name}' as a new client, or would you prefer to enter a different client name?",
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                )
